-- Table: users
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Unique User ID',
  `username` VA<PERSON>HA<PERSON>(255) NOT NULL COMMENT 'Username',
  `password_hash` VA<PERSON>HA<PERSON>(255) NOT NULL COMMENT 'Hashed password',
  `email` VARCHAR(255) NULL UNIQUE COMMENT 'User email address',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record Creation Time',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record Last Update Time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user accounts';

-- Table: user_auth_tokens
CREATE TABLE `user_auth_tokens` (
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT 'Linked User ID from users table',
  `token_hash` VARCHAR(64) NOT NULL COMMENT 'SHA-256 Hashed Game Token',
  `expires_at` TIMESTAMP NOT NULL COMMENT 'Token Expiration Timestamp',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`, `token_hash`),
  UNIQUE KEY `idx_token_hash` (`token_hash`),
  INDEX `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores active game login token hashes';

-- Table: friend_relationships
CREATE TABLE `friend_relationships` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_one_id` BIGINT UNSIGNED NOT NULL COMMENT 'Smaller user ID, ensures uniqueness for the pair',
  `user_two_id` BIGINT UNSIGNED NOT NULL COMMENT 'Larger user ID, ensures uniqueness for the pair',
  `status` ENUM('pending', 'accepted', 'declined', 'blocked', 'deleted') NOT NULL COMMENT 'Relationship status: pending, accepted, declined, blocked (by action_user_id), deleted (unfriended)',
  `action_user_id` BIGINT UNSIGNED NOT NULL COMMENT 'User ID who performed the last action (e.g., sent request, accepted, blocked)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique_relationship` (`user_one_id`, `user_two_id`),
  INDEX `idx_user_one_id_status` (`user_one_id`, `status`),
  INDEX `idx_user_two_id_status` (`user_two_id`, `status`),
  INDEX `idx_status_updated_at` (`status`, `updated_at`),
  FOREIGN KEY (`user_one_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_two_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`action_user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores friendships and block relationships between users';

-- Table: characters
CREATE TABLE `characters` (
  `character_id` VARCHAR(36) NOT NULL COMMENT 'Unique character ID (UUID)',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT 'User ID owning the character',
  `character_name` VARCHAR(50) NOT NULL COMMENT 'Character name',
  `level` INT NOT NULL DEFAULT 1,
  `experience` BIGINT NOT NULL DEFAULT 0,
  `pos_x` FLOAT NOT NULL DEFAULT 0.0,
  `pos_y` FLOAT NOT NULL DEFAULT 0.0,
  `current_scene_id` VARCHAR(255) NOT NULL DEFAULT 'default_starting_zone',
  `selected_appearance_details` JSON NOT NULL COMMENT 'Character appearance details, JSON format',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`character_id`),
  UNIQUE KEY `idx_character_name_unique` (`character_name`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_user_id_level` (`user_id`, `level`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player characters';

-- Table: items (Item Templates)
CREATE TABLE `items` (
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Unique Item template ID (e.g., "basic_chair", "tomato_seed")',
  `name` VARCHAR(255) NOT NULL COMMENT 'Display name of the item',
  `description` TEXT NULL COMMENT 'Description of the item',
  `type` ENUM('furniture', 'seed', 'crop', 'tool', 'wearable', 'consumable', 'material', 'background', 'plot_template') NOT NULL COMMENT 'Broad category of the item',
  `category` VARCHAR(255) NULL COMMENT 'Specific category (e.g., "decor", "chair", "vegetable", "hat", "potion")',
  `is_stackable` BOOLEAN NOT NULL DEFAULT FALSE,
  `max_stack_size` INT NOT NULL DEFAULT 1,
  `data` JSON NULL COMMENT 'Additional item-specific data (e.g., dimensions for furniture, growth time for seeds, effect for consumables, allowed_scenes for backgrounds)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_id`),
  INDEX `idx_item_type` (`type`),
  INDEX `idx_item_category` (`category`),
  INDEX `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores item templates and definitions';

-- Table: user_private_spaces (Settings for Home & Garden)
CREATE TABLE `user_private_spaces` (
  `owner_user_id` BIGINT UNSIGNED NOT NULL COMMENT 'Linked to users table ID',
  `home_background_id` VARCHAR(255) NOT NULL DEFAULT 'default_home_bg',
  `garden_background_id` VARCHAR(255) NOT NULL DEFAULT 'default_garden_bg',
  `home_access_level` ENUM('private', 'friends_only', 'public') NOT NULL DEFAULT 'private',
  `garden_access_level` ENUM('private', 'friends_only', 'public') NOT NULL DEFAULT 'private',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`home_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`garden_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores settings for user''s private home and garden spaces';

-- Table: home_items (Instances of Furniture in Homes)
CREATE TABLE `home_items` (
  `item_instance_id` VARCHAR(36) NOT NULL COMMENT 'Unique instance ID for this placed item (UUID)',
  `owner_user_id` BIGINT UNSIGNED NOT NULL COMMENT 'User ID who owns this home and item instance',
  `item_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID from the items table',
  `pos_x` FLOAT NOT NULL,
  `pos_y` FLOAT NOT NULL,
  `rotation` FLOAT NOT NULL DEFAULT 0.0,
  `is_flipped` BOOLEAN NOT NULL DEFAULT FALSE,
  `placed_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when the item was placed',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_instance_id`),
  INDEX `idx_owner_user_id` (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of items placed in users'' homes';

-- Table: garden_plots (Instances of Garden Plots)
CREATE TABLE `garden_plots` (
  `plot_instance_id` VARCHAR(255) NOT NULL COMMENT 'Unique ID for this plot instance (e.g., owner_user_id_plot_index or UUID)',
  `owner_user_id` BIGINT UNSIGNED NOT NULL COMMENT 'User ID who owns this garden plot instance',
  `plot_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID for the plot itself (type ''plot_template'' from items table)',
  `seed_item_id` VARCHAR(255) NULL COMMENT 'Template ID of the seed currently planted (type ''seed'' from items table)',
  `plant_timestamp` TIMESTAMP NULL DEFAULT NULL COMMENT 'Timestamp when the current seed was planted',
  `growth_stage` INT NOT NULL DEFAULT 0 COMMENT 'Current growth stage of the plant (e.g., 0:empty, 1:seeded, 2:sprouting, ...)',
  `last_watered_timestamp` TIMESTAMP NULL DEFAULT NULL COMMENT 'Timestamp when the plot was last watered',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`plot_instance_id`),
  INDEX `idx_owner_user_id` (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`plot_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`seed_item_id`) REFERENCES `items`(`item_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of garden plots in users'' gardens';

-- Table: user_inventory (Tracks items owned by users)
CREATE TABLE `user_inventory` (
  `inventory_id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` BIGINT UNSIGNED NOT NULL,
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Template ID from the items table',
  `quantity` INT UNSIGNED NOT NULL DEFAULT 1,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`inventory_id`),
  UNIQUE KEY `idx_user_item_unique` (`user_id`, `item_id`),
  INDEX `idx_item_id` (`item_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user item inventories';

-- Table: user_character_selection (Tracks which character a user has currently selected)
CREATE TABLE `user_character_selection` (
  `user_id` BIGINT UNSIGNED NOT NULL,
  `selected_character_id` VARCHAR(36) NOT NULL,
  `selected_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`selected_character_id`) REFERENCES `characters`(`character_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores the currently selected character for each user';

-- Table: game_settings (Global game settings or feature flags)
CREATE TABLE `game_settings` (
  `setting_key` VARCHAR(100) NOT NULL,
  `setting_value` TEXT NULL,
  `description` VARCHAR(255) NULL,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores global game settings and feature flags';

-- Table: chat_messages (Persistent chat log, optional)
CREATE TABLE `chat_messages` (
  `message_id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `sender_user_id` BIGINT UNSIGNED NOT NULL,
  `sender_character_id` VARCHAR(36) NULL COMMENT 'Character ID of the sender, if applicable',
  `room_id` VARCHAR(255) NOT NULL COMMENT 'Room or channel ID where message was sent',
  `message_content` TEXT NOT NULL,
  `sent_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`message_id`),
  INDEX `idx_room_id_sent_at` (`room_id`, `sent_at`),
  INDEX `idx_sender_user_id` (`sender_user_id`),
  INDEX `idx_sent_at` (`sent_at`),
  FOREIGN KEY (`sender_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`sender_character_id`) REFERENCES `characters`(`character_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores chat messages for logging or persistence';
