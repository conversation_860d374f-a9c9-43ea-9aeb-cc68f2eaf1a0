import { Room, Client } from '@colyseus/core';
import { Schema, MapSchema, type } from '@colyseus/schema';
import authService from '../services/authService'; // Your AuthService
import { PlayerState } from '../models/PlayerState'; // Player state schema
import { ChatMessage } from '../models/ChatMessage'; // Chat message schema
import { v4 as uuidv4 } from 'uuid'; // For generating unique IDs if needed for guests or fallback

// Define the state for the Public Lobby Room
export class LobbyRoomState extends Schema {
  @type({ map: PlayerState })
  players = new MapSchema<PlayerState>();

  @type('number')
  playerCount: number = 0;
}

export class PublicLobbyRoom extends Room<LobbyRoomState> {
  maxClients = 100; // Example: Maximum number of clients in this room

  async onCreate(options: any) {
    console.log('PublicLobbyRoom created!', options);
    this.setState(new LobbyRoomState());

    // Handler for player state updates from clients
    this.onMessage('updatePlayerState', (client, data) => {
      this.handlePlayerStateUpdate(client, data);
    });

    // Handler for chat messages from clients
    this.onMessage('chat', (client, data) => {
      this.handleChatMessage(client, data);
    });
  }

  async onAuth(client: Client, options: any, request: any) {
    console.log(`Attempting auth for client ${client.sessionId} with options:`, options);
    if (!options || !options.token || !options.uid) {
      console.error(`Auth failed for client ${client.sessionId}: Missing token or uid.`);
      throw new Error('Authentication failed: Token and UID are required.');
    }

    const { token, uid } = options;
    const userPayload = await authService.verifyToken(token);

    if (userPayload && userPayload.userId && userPayload.userId.toString() === uid.toString()) {
      console.log(`Auth successful for client ${client.sessionId}, UID: ${uid}`);
      return { uid: uid.toString(), isAuthenticated: true, token }; // Pass UID and auth status
    } else {
      console.error(`Auth failed for client ${client.sessionId}: Invalid token or UID mismatch.`);
      throw new Error('Authentication failed: Invalid token or UID.');
    }
  }

  onJoin(client: Client, options: any, auth: any) {
    if (!auth || !auth.uid || !auth.isAuthenticated) {
        console.error(`Join rejected for client ${client.sessionId}: Not authenticated or missing UID.`);
        // This should ideally not happen if onAuth is strict
        client.leave(1001, "Authentication failed or UID missing."); 
        return;
    }
    
    console.log(`${client.sessionId} (UID: ${auth.uid}) joined PublicLobbyRoom.`);

    // Create a new PlayerState instance
    // For initial position, you might want a system to prevent overlaps or define spawn points
    const initialX = Math.floor(Math.random() * 800); // Example random X
    const initialY = Math.floor(Math.random() * 600); // Example random Y
    
    const player = new PlayerState(client.sessionId, auth.uid, initialX, initialY);

    // Example: Set a default or random outfit (can be fetched from a DB later)
    player.currentOutfit = JSON.stringify({
      body: 'standard_male',
      hair: 'brown_short_hair',
      top: 'blue_tshirt',
      bottom: 'jeans_regular',
      shoes: 'sneakers_white'
    });
    player.currentAnimation = 'idle';
    player.dir = 'down';

    this.state.players.set(client.sessionId, player);
    this.state.playerCount = this.clients.length;

    // Send a welcome message to the joined client
    client.send('welcome', { 
      message: `Welcome to the Public Lobby, ${auth.uid}!`,
      sessionId: client.sessionId,
      initialPlayers: Array.from(this.state.players.values()).map(p => ({
        sessionId: p.sessionId,
        uid: p.uid,
        x: p.x,
        y: p.y,
        dir: p.dir,
        isFlipped: p.isFlipped,
        isSitting: p.isSitting,
        currentAnimation: p.currentAnimation,
        currentOutfit: p.currentOutfit,
      }))
    });

    // Broadcast to other clients that a new player has joined
    this.broadcast('player_joined', {
      sessionId: client.sessionId,
      uid: auth.uid,
      x: player.x,
      y: player.y,
      dir: player.dir,
      isFlipped: player.isFlipped,
      isSitting: player.isSitting,
      currentAnimation: player.currentAnimation,
      currentOutfit: player.currentOutfit,
    }, { except: client }); // Send to all except the joining client
  }

  handlePlayerStateUpdate(client: Client, data: any) {
    const player = this.state.players.get(client.sessionId);
    if (player) {
      // Update only allowed fields to prevent malicious data injection
      if (data.x !== undefined) player.x = Number(data.x);
      if (data.y !== undefined) player.y = Number(data.y);
      if (data.dir !== undefined) player.dir = String(data.dir);
      if (data.isFlipped !== undefined) player.isFlipped = Boolean(data.isFlipped);
      if (data.isSitting !== undefined) player.isSitting = Boolean(data.isSitting);
      if (data.currentAnimation !== undefined) player.currentAnimation = String(data.currentAnimation);
      
      // For currentOutfit, ensure it's a valid JSON string or handle parsing errors
      if (data.currentOutfit !== undefined) {
        try {
          // Basic validation: check if it's a string and parsable
          if (typeof data.currentOutfit === 'string') {
             JSON.parse(data.currentOutfit); // Try to parse to check validity
             player.currentOutfit = data.currentOutfit;
          } else if (typeof data.currentOutfit === 'object') { // If client sends an object
             player.currentOutfit = JSON.stringify(data.currentOutfit);
          }
        } catch (e) {
          console.warn(`Client ${client.sessionId} sent invalid outfit data: ${data.currentOutfit}`);
        }
      }
      // Broadcasting the full player state or just the delta is a design choice.
      // For simplicity, broadcasting the changed fields or the whole player object.
      // For optimization, consider sending only deltas or using patch operations.
      this.broadcast('player_state_updated', { sessionId: client.sessionId, ...data }, { except: client });
    }
  }

  handleChatMessage(client: Client, data: { message: string, channel?: string }) {
    const player = this.state.players.get(client.sessionId);
    if (!player || !data || typeof data.message !== 'string' || data.message.trim() === '') {
      console.log(`Invalid chat message from ${client.sessionId}`);
      // Optionally send an error back to the client
      // client.send('chat_error', { message: 'Invalid message content.' });
      return;
    }

    const messageContent = data.message.trim();
    // Basic sanitization or length check (more robust checks might be needed)
    if (messageContent.length > 256) { 
      // client.send('chat_error', { message: 'Message too long.' });
      return;
    }
    
    const channel = (typeof data.channel === 'string' && data.channel.trim() !== '') ? data.channel.trim() : 'global';

    const chatMessage = new ChatMessage(
      client.sessionId,
      player.uid, // Use UID from the authenticated player state
      messageContent,
      channel
    );

    // Broadcast the chat message to all clients in the room
    this.broadcast('new_chat_message', chatMessage.toJSON()); 
    console.log(`Chat from ${player.uid} (${client.sessionId}) in #${channel}: ${messageContent}`);
  }

  onLeave(client: Client, consented: boolean) {
    const player = this.state.players.get(client.sessionId);
    const uid = player ? player.uid : 'Unknown';

    if (player) {
      this.state.players.delete(client.sessionId);
      this.state.playerCount = this.clients.length; // Update count based on connected clients

      console.log(`${client.sessionId} (UID: ${uid}) left PublicLobbyRoom.`);
      this.broadcast('player_left', { sessionId: client.sessionId, uid: uid });
    } else {
      console.log(`${client.sessionId} (UID: ${uid}) left PublicLobbyRoom, but was not in state.`);
    }
  }

  onDispose() {
    console.log('PublicLobbyRoom disposed.');
  }
}
