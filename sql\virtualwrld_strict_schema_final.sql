-- Table: users (Plan Section 4.1.1.1)
CREATE TABLE `users` (
  `id` BIGINT AUTO_INCREMENT COMMENT 'Unique User ID',
  `username` VARCHA<PERSON>(255) NOT NULL COMMENT 'Username',
  `password_hash` VA<PERSON><PERSON><PERSON>(255) NOT NULL COMMENT 'Hashed password',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record Creation Time',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record Last Update Time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user accounts';

-- Table: user_auth_tokens (Plan Section 4.1.1.2)
CREATE TABLE `user_auth_tokens` (
  `uid` BIGINT NOT NULL COMMENT 'Linked User ID from users table',
  `token` VARCHAR(255) NOT NULL COMMENT 'SHA256 Hashed Game Token',
  `expires_at` BIGINT UNSIGNED NOT NULL COMMENT 'Token Expiration Timestamp (Unix milliseconds)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`uid`, `token`),
  UNIQUE KEY `idx_token` (`token`),
  FOREIGN KEY (`uid`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores active game login tokens';

-- Table: friend_relationships (Plan Section 4.3.1)
CREATE TABLE `friend_relationships` (
  `id` BIGINT AUTO_INCREMENT,
  `user_one_id` BIGINT NOT NULL COMMENT 'Smaller user ID',
  `user_two_id` BIGINT NOT NULL COMMENT 'Larger user ID',
  `status` ENUM('pending', 'accepted', 'declined', 'blocked') NOT NULL COMMENT 'Relationship status',
  `action_user_id` BIGINT NOT NULL COMMENT 'User ID who performed the last action',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_relationship` (`user_one_id`, `user_two_id`),
  INDEX `idx_user_one_status` (`user_one_id`, `status`),
  INDEX `idx_user_two_status` (`user_two_id`, `status`),
  FOREIGN KEY (`user_one_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`user_two_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`action_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores friendships and block relationships';

-- Table: characters (Plan Section 4.4.1)
CREATE TABLE `characters` (
  `character_id` BIGINT AUTO_INCREMENT COMMENT 'Unique character ID',
  `user_id` BIGINT NOT NULL COMMENT 'User ID owning the character',
  `character_name` VARCHAR(255) NOT NULL COMMENT 'Character name',
  `level` INT NOT NULL DEFAULT 1,
  `experience` BIGINT NOT NULL DEFAULT 0,
  `pos_x` FLOAT NOT NULL DEFAULT 0.0,
  `pos_y` FLOAT NOT NULL DEFAULT 0.0,
  `current_scene_id` VARCHAR(255) NOT NULL,
  `selected_appearance_details` JSON NOT NULL COMMENT 'Character appearance details, JSON format',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`character_id`),
  INDEX `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores player characters';

-- Table: items (Item Templates) (Plan Section *******)
CREATE TABLE `items` (
  `item_id` VARCHAR(255) NOT NULL COMMENT 'Unique Item ID (e.g., "basic_chair", "tomato_seed")',
  `item_name` VARCHAR(255) NOT NULL COMMENT 'Display name of the item',
  `description` TEXT COMMENT 'Description of the item',
  `item_type` ENUM('furniture', 'plant', 'seed', 'tool', 'wearable', 'consumable', 'collectible') NOT NULL COMMENT 'Broad category of the item',
  `category` VARCHAR(255) COMMENT 'Specific category (e.g., "decor", "chair", "vegetable", "hat", "potion")',
  `price` INT UNSIGNED COMMENT 'Price in in-game currency (if applicable)',
  `is_stackable` BOOLEAN DEFAULT FALSE,
  `max_stack_size` INT DEFAULT 1,
  `total_growth_time_ms` INT UNSIGNED COMMENT 'For plants/seeds: total time from planting to harvestable in milliseconds',
  `num_growth_stages` INT UNSIGNED COMMENT 'For plants/seeds: number of distinct visual growth stages',
  `water_interval_ms` INT UNSIGNED COMMENT 'For plants/seeds: how often it needs watering in milliseconds',
  `harvest_details` JSON COMMENT 'For plants/seeds: JSON detailing item(s) and quantities yielded upon harvest',
  `data` JSON COMMENT 'Additional item-specific data (e.g., dimensions for furniture, effect for consumables)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores item templates and definitions';

-- Table: user_private_spaces (Settings for Home & Garden) (Plan Section 4.5.2.1)
CREATE TABLE `user_private_spaces` (
  `owner_user_id` BIGINT NOT NULL COMMENT 'Linked to users table ID',
  `home_background_id` VARCHAR(255) DEFAULT 'default_home_bg',
  `garden_background_id` VARCHAR(255) DEFAULT 'default_garden_bg',
  `home_access_level` ENUM('private', 'friends_only', 'public') DEFAULT 'private',
  `garden_access_level` ENUM('private', 'friends_only', 'public') DEFAULT 'private',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`home_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`garden_background_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores settings for user''s private home and garden spaces';

-- Table: home_items (Instances of Furniture in Homes) (Plan Section *******)
CREATE TABLE `home_items` (
  `item_instance_id` VARCHAR(36) NOT NULL COMMENT 'Unique instance ID for this placed item (UUID)',
  `owner_user_id` BIGINT NOT NULL COMMENT 'User ID who owns this home and item instance',
  `item_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID from the items table',
  `pos_x` FLOAT NOT NULL,
  `pos_y` FLOAT NOT NULL,
  `rotation` FLOAT DEFAULT 0.0,
  `is_flipped` BOOLEAN DEFAULT FALSE,
  `placed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`item_instance_id`),
  INDEX `idx_owner_user_id` (`owner_user_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`item_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of items placed in users'' homes';

-- Table: garden_plots (Instances of Garden Plots) (Plan Section *******)
CREATE TABLE `garden_plots` (
  `plot_id` VARCHAR(255) NOT NULL COMMENT 'Unique ID for this plot instance (e.g., owner_user_id_plot_index)',
  `owner_user_id` BIGINT NOT NULL COMMENT 'User ID who owns this garden plot instance',
  `plot_template_id` VARCHAR(255) NOT NULL COMMENT 'Template ID for the plot itself (e.g., "small_plot")',
  `seed_id` VARCHAR(255) COMMENT 'Template ID of the seed currently planted',
  `plant_timestamp` BIGINT UNSIGNED COMMENT 'Unix timestamp (ms) when current seed was planted',
  `growth_stage` INT DEFAULT 0 COMMENT 'Current growth stage of the plant',
  `last_watered_timestamp` BIGINT UNSIGNED COMMENT 'Unix timestamp (ms) when plot was last watered',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`plot_id`),
  UNIQUE KEY `idx_owner_plot` (`owner_user_id`, `plot_id`),
  FOREIGN KEY (`owner_user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`plot_template_id`) REFERENCES `items`(`item_id`) ON DELETE RESTRICT,
  FOREIGN KEY (`seed_id`) REFERENCES `items`(`item_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores instances of garden plots in users'' gardens';
