import pool from '../utils/db'; // Database connection pool utility
import { RowDataPacket, ResultSetHeader } from 'mysql2';

/**
 * @export
 * @interface ItemDetails
 * @description Represents the detailed structure of an item template from the `items` table.
 * @property {string} item_id - The unique identifier for the item template (e.g., "basic_chair", "tomato_seed").
 * @property {string} name - The display name of the item.
 * @property {string | null} description - A text description of the item.
 * @property {'furniture' | 'seed' | 'crop' | 'tool' | 'wearable' | 'consumable' | 'material' | 'background' | 'plot_template'} type - The broad category of the item.
 * @property {string | null} category - A more specific category (e.g., "decor", "chair", "vegetable", "hat").
 * @property {boolean} is_stackable - Whether the item can be stacked in inventory.
 * @property {number} max_stack_size - The maximum number of items in a single stack.
 * @property {string | null} data - A JSON string for additional item-specific data (e.g., dimensions, growth times, effects).
 */
export interface ItemDetails extends RowDataPacket {
  item_id: string;
  name: string;
  description: string | null;
  type: 'furniture' | 'seed' | 'crop' | 'tool' | 'wearable' | 'consumable' | 'material' | 'background' | 'plot_template';
  category: string | null;
  is_stackable: boolean;
  max_stack_size: number;
  data: string | null; // JSON string
}

/**
 * @class ItemService
 * @description Provides methods for interacting with item data and simulated inventory.
 *              Implemented as a singleton.
 */
class ItemService {
  private static instance: ItemService;

  /**
   * Private constructor to enforce singleton pattern.
   * @private
   */
  private constructor() {
    // Initialization logic if needed
  }

  /**
   * Gets the singleton instance of the ItemService.
   * @public
   * @static
   * @returns {ItemService} The singleton instance.
   */
  public static getInstance(): ItemService {
    if (!ItemService.instance) {
      ItemService.instance = new ItemService();
    }
    return ItemService.instance;
  }

  /**
   * Fetches details for a specific item template from the database.
   * @param {string} itemId - The template ID of the item to fetch.
   * @returns {Promise<ItemDetails | null>} A promise that resolves with the item details if found, otherwise null.
   * @throws {Error} If `itemId` is not provided or if a database error occurs.
   */
  async getItemDetails(itemId: string): Promise<ItemDetails | null> {
    if (!itemId || typeof itemId !== 'string') {
      throw new Error('ItemService: getItemDetails - itemId must be a non-empty string.');
    }
    try {
      const [rows] = await pool.query<ItemDetails[]>(
        'SELECT * FROM items WHERE item_id = ?',
        [itemId]
      );
      if (rows.length > 0) {
        return rows[0]; // Returns the first found item
      }
      // If no item is found, it's a valid case, so return null, not an error.
      // The caller can then decide if this constitutes an error in their context.
      console.log(`ItemService: No item found with itemId '${itemId}'.`);
      return null;
    } catch (error: any) {
      console.error(`ItemService: Error fetching item details for itemId '${itemId}': ${error.message}`);
      // Re-throw a more generic error to avoid exposing DB details.
      throw new Error(`Failed to fetch details for item '${itemId}' due to a server error.`);
    }
  }

  /**
   * Simulates checking if a user has sufficient quantity of an item and "uses" it (decrements quantity).
   * In a real system, this would interact with a `user_inventory` table.
   * @param {string} userId - The ID of the user whose inventory is being checked.
   * @param {string} itemId - The ID of the item template to check and use.
   * @param {number} [quantity=1] - The quantity of the item to use. Defaults to 1.
   * @returns {Promise<boolean>} A promise that resolves to true if the user has the item and it was "used", false otherwise.
   * @throws {Error} If `userId` or `itemId` is invalid, quantity is not positive, or if a database/item validation error occurs.
   */
  async checkAndUseInventoryItem(userId: string, itemId: string, quantity: number = 1): Promise<boolean> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('ItemService: checkAndUseInventoryItem - userId must be a non-empty string.');
    }
    if (!itemId || typeof itemId !== 'string') {
      throw new Error('ItemService: checkAndUseInventoryItem - itemId must be a non-empty string.');
    }
    if (typeof quantity !== 'number' || quantity <= 0) {
      throw new Error('ItemService: checkAndUseInventoryItem - quantity must be a positive number.');
    }

    console.log(`ItemService (Simulated): Checking and attempting to use ${quantity} of item '${itemId}' for user '${userId}'.`);
    
    // 1. Verify item exists
    const itemDetails = await this.getItemDetails(itemId);
    if (!itemDetails) {
      // This error indicates the item template itself doesn't exist, which is a problem.
      throw new Error(`ItemService: Cannot use item '${itemId}' because it does not exist in the items table.`);
    }

    // 2. Simulate inventory check & update
    // In a real system:
    // START TRANSACTION;
    // SELECT quantity FROM user_inventory WHERE user_id = ? AND item_id = ? FOR UPDATE;
    // IF row_not_found OR selected_quantity < quantity THEN ROLLBACK; RETURN false / throw Error('Insufficient quantity');
    // UPDATE user_inventory SET quantity = quantity - ? WHERE user_id = ? AND item_id = ?; (or DELETE if quantity becomes 0 and item is not meant to persist with 0 qty)
    // COMMIT;
    // RETURN true;
    // CATCH error: ROLLBACK; throw error;
    
    // For this simulation, assume the user always has enough of any existing item.
    // This part needs to be replaced with actual database logic against `user_inventory`.
    const mockInventoryQuantity = 100; // Assume user has 100 of everything for simulation
    if (mockInventoryQuantity < quantity) {
      console.warn(`ItemService (Simulated): User '${userId}' has insufficient quantity of item '${itemId}'. Needed: ${quantity}, Has: ${mockInventoryQuantity}.`);
      // In a real system, this would be an error or return false.
      // For this simulation, let's throw an error to match the "throws errors if an action cannot be performed" requirement.
      throw new Error(`Insufficient quantity of item '${itemId}' in inventory for user '${userId}'.`);
    }

    console.log(`ItemService (Simulated): User '${userId}' "used" ${quantity} of item '${itemId}'. Inventory would be updated.`);
    return true; 
  }

   /**
   * Simulates adding an item to a user's inventory.
   * In a real system, this would interact with a `user_inventory` table.
   * @param {string} userId - The ID of the user to whose inventory the item will be added.
   * @param {string} itemId - The ID of the item template to add.
   * @param {number} [quantity=1] - The quantity of the item to add. Defaults to 1.
   * @returns {Promise<boolean>} A promise that resolves to true if the item was "added" successfully.
   * @throws {Error} If `userId` or `itemId` is invalid, quantity is not positive, or if a database/item validation error occurs.
   */
  async addInventoryItem(userId: string, itemId: string, quantity: number = 1): Promise<boolean> {
    if (!userId || typeof userId !== 'string') {
      throw new Error('ItemService: addInventoryItem - userId must be a non-empty string.');
    }
    if (!itemId || typeof itemId !== 'string') {
      throw new Error('ItemService: addInventoryItem - itemId must be a non-empty string.');
    }
    if (typeof quantity !== 'number' || quantity <= 0) {
      throw new Error('ItemService: addInventoryItem - quantity must be a positive number.');
    }
    
    console.log(`ItemService (Simulated): Attempting to add ${quantity} of item '${itemId}' to inventory for user '${userId}'.`);

    // 1. Verify item template exists
    const itemDetails = await this.getItemDetails(itemId);
    if (!itemDetails) {
      throw new Error(`ItemService: Cannot add item '${itemId}' to inventory because it does not exist in the items table.`);
    }

    // 2. Simulate inventory update
    // In a real system:
    // START TRANSACTION;
    // SELECT quantity, is_stackable, max_stack_size FROM items WHERE item_id = ?; (already got itemDetails)
    // SELECT quantity FROM user_inventory WHERE user_id = ? AND item_id = ? FOR UPDATE;
    // IF row_found AND itemDetails.is_stackable:
    //   IF current_quantity + quantity > itemDetails.max_stack_size AND itemDetails.max_stack_size > 0 (0 could mean infinite stack)
    //     THROW Error('Cannot add item, exceeds max stack size for this slot/item combination.'); // Or handle by creating new stacks
    //   UPDATE user_inventory SET quantity = quantity + ? WHERE user_id = ? AND item_id = ?;
    // ELSE (new item entry or not stackable, so always new row for non-stackable unless specific slotting exists):
    //   INSERT INTO user_inventory (user_id, item_id, quantity) VALUES (?, ?, ?);
    // COMMIT;
    // RETURN true;
    // CATCH error: ROLLBACK; throw error;
    
    console.log(`ItemService (Simulated): Item '${itemId}' (quantity ${quantity}) "added" to user '${userId}'s inventory. Inventory would be updated.`);
    return true;
  }
}

// Export a singleton instance of ItemService.
export default ItemService.getInstance();
