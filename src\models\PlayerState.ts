import { Schema, type } from '@colyseus/schema';

export class PlayerState extends Schema {
  @type('string') sessionId: string = ''; // The client's sessionId
  @type('string') uid: string = ''; // User ID from your authentication system
  @type('number') x: number = 0;
  @type('number') y: number = 0;
  @type('string') dir: string = 'down'; // e.g., 'up', 'down', 'left', 'right'
  @type('boolean') isFlipped: boolean = false; // For sprite flipping
  @type('boolean') isSitting: boolean = false;
  @type('string') currentAnimation: string = 'idle'; // e.g., 'idle', 'walk', 'sit'
  
  // Example: Outfit structure (can be more complex)
  @type('string') currentOutfit: string = JSON.stringify({
    body: 'default_body',
    hair: 'default_hair',
    top: 'default_top',
    bottom: 'default_bottom',
    shoes: 'default_shoes'
  }); // JSON string for outfit details. Parsed by client/server when accessed.
      // Consider a nested Schema for `currentOutfit` if individual parts need to be synced
      // or if the structure becomes very complex.

  constructor(sessionId: string, uid: string, x: number = 0, y: number = 0) {
    super();
    this.sessionId = sessionId;
    this.uid = uid;
    this.x = x;
    this.y = y;
  }
}
